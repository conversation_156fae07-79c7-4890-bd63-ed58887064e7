# Docker Pussh 故障排除指南

## 问题描述
从 inf246 推送到 iris 失败，错误信息：
```
Get "http://localhost:56354/v2/": read tcp 127.0.0.1:59516->127.0.0.1:56354: read: connection reset by peer
```

## 可能的原因分析

### 1. 网络连接问题
- SSH 端口转发不稳定
- 防火墙或网络策略阻止连接
- 本地端口冲突

### 2. Docker 配置问题
- 代理设置干扰本地连接
- Docker 版本兼容性问题
- containerd 配置问题

### 3. 时序问题
- unregistry 容器启动时间不够
- 端口转发建立时间不够

## 解决方案

### 步骤 1: 使用调试版本
使用增强的调试版本 `docker-pussh-debug`：

```bash
# 复制调试版本到 Docker 插件目录
cp docker-pussh-debug ~/.docker/cli-plugins/docker-pussh-debug
chmod +x ~/.docker/cli-plugins/docker-pussh-debug

# 启用调试模式运行
UNREGISTRY_DEBUG=1 ./docker-pussh-debug docker.io/infiniflow/infinity_builder:centos7_clang18 zhichyu@***************
```

### 步骤 2: 手动测试连接
在运行 pussh 之前，手动测试 SSH 连接：

```bash
# 测试基本 SSH 连接
ssh zhichyu@*************** "echo 'SSH connection works'"

# 测试 Docker 权限
ssh zhichyu@*************** "docker version"

# 测试端口转发
ssh -L 8080:localhost:80 zhichyu@*************** -N &
curl http://localhost:8080  # 应该连接到远程的 80 端口
kill %1  # 终止后台 SSH 进程
```

### 步骤 3: 检查系统配置

#### 在 inf246 上检查：
```bash
# 检查 Docker 配置
docker info | grep -i proxy
docker info | grep -i containerd

# 检查网络连接
netstat -tuln | grep :55000-65535  # 检查端口占用

# 检查防火墙
sudo ufw status
sudo iptables -L | grep DROP
```

#### 在 iris 上检查：
```bash
# 检查 Docker 服务状态
systemctl status docker
sudo journalctl -u docker --since "1 hour ago"

# 检查 containerd 套接字
ls -la /run/containerd/containerd.sock
sudo chmod 666 /run/containerd/containerd.sock  # 如果权限有问题

# 检查端口可用性
ss -tuln | grep :55000-65535
```

### 步骤 4: 逐步调试

#### 4.1 测试 unregistry 容器
```bash
# 在 iris 上手动启动 unregistry
docker run -d --name test-unregistry \
  -p 127.0.0.1:55000:5000 \
  -v /run/containerd/containerd.sock:/run/containerd/containerd.sock \
  --userns=host --user root:root \
  ghcr.io/psviderski/unregistry:0.1.3

# 测试连接
curl http://localhost:55000/v2/

# 查看日志
docker logs test-unregistry

# 清理
docker rm -f test-unregistry
```

#### 4.2 测试端口转发
```bash
# 从 inf246 建立端口转发到 iris
ssh -L 55001:localhost:55000 zhichyu@*************** -N &

# 在另一个终端测试
curl http://localhost:55001/v2/

# 清理
kill %1
```

### 步骤 5: 临时解决方案

如果问题持续存在，可以尝试以下临时解决方案：

#### 5.1 增加重试和延迟
修改脚本中的重试参数：
```bash
PUSH_RETRY_COUNT=5  # 增加重试次数
sleep 5  # 增加等待时间
```

#### 5.2 使用固定端口
避免随机端口可能的冲突：
```bash
# 在脚本中使用固定端口范围
UNREGISTRY_PORT=55000
LOCAL_PORT=55001
```

#### 5.3 禁用 Docker Desktop 检测
如果不使用 Docker Desktop，强制禁用额外隧道：
```bash
# 在脚本中注释掉或修改
# if is_additional_tunneling_needed; then
if false; then
```

## 调试输出解读

### 正常输出应该包含：
```
Debug: Unregistry container unregistry-pussh-xxx started successfully
Debug: Unregistry is responding on remote host
Debug: Local port forwarding is working
Debug: Push attempt 1 of 3
```

### 异常输出可能包含：
```
Debug: Unregistry may not be responding properly on remote host
Debug: Local port forwarding may not be working properly
Debug: Testing connectivity before retry...
```

## 进一步调试

如果问题仍然存在，收集以下信息：

1. **完整的调试输出**
2. **两台机器的网络配置**
3. **Docker 和 containerd 版本信息**
4. **系统日志中的相关错误**

```bash
# 收集系统信息
uname -a
docker version
docker info
systemctl status docker
sudo journalctl -u docker --since "1 hour ago"
```

## 已知问题和限制

1. **OpenSUSE Tumbleweed 特殊性**：某些网络配置可能与 Ubuntu 不同
2. **Docker 版本差异**：inf246 使用 24.0.7，iris 使用 28.2.2
3. **代理配置残留**：即使删除了 daemon 配置，环境变量可能仍然存在

## 联系支持

如果以上步骤都无法解决问题，请提供：
- 完整的调试输出
- 两台机器的 `docker info` 输出
- 网络配置信息
- 系统日志相关部分
