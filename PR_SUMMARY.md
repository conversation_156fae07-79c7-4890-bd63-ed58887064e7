# Fix containerd socket detection and connection issues

## Problem

The `docker pussh` command was failing with "connection reset by peer" errors when pushing from certain systems, particularly when the containerd socket was not located at the default path `/run/containerd/containerd.sock`.

## Root Cause

1. **Incorrect containerd socket path**: Different Docker installations (especially Docker-managed containerd) place the socket at `/var/run/docker/containerd/containerd.sock` instead of the assumed `/run/containerd/containerd.sock`
2. **Permission issues**: Regular users may not have access to the containerd socket without sudo
3. **Proxy interference**: HTTP proxy settings could interfere with localhost connections
4. **Insufficient container startup verification**: The script didn't verify that the unregistry container was actually running after creation

## Solution

### 1. Dynamic containerd socket detection
Added `find_containerd_socket()` function that:
- Checks multiple common socket paths in priority order:
  - `/var/run/docker/containerd/containerd.sock` (Docker-managed containerd)
  - `/run/containerd/containerd.sock` (standalone containerd)
  - `/var/run/containerd/containerd.sock`
  - `/run/docker/containerd/containerd.sock`
- Falls back to dynamic discovery using `find` command
- Handles permission issues by trying both regular and sudo access

### 2. Improved container startup verification
- Added verification that the unregistry container is actually running after creation
- Added retry logic for container startup failures with proper cleanup

### 3. Proxy handling
- Temporarily disable proxy environment variables during push operations
- Restore proxy settings after completion
- Apply same proxy handling to remote docker pull operations

### 4. Enhanced retry mechanism
- Added retry logic for push operations with 3-second delays
- Better error handling and user feedback
- Graceful failure after multiple attempts

## Code Changes

### Key functions added/modified:

```bash
# New function for dynamic socket detection
find_containerd_socket() {
    local socket_paths=(
        "/var/run/docker/containerd/containerd.sock"
        "/run/containerd/containerd.sock"
        "/var/run/containerd/containerd.sock"
        "/run/docker/containerd/containerd.sock"
    )
    
    for socket_path in "${socket_paths[@]}"; do
        if ssh "${SSH_ARGS[@]}" "test -S $socket_path" 2>/dev/null || 
           ssh "${SSH_ARGS[@]}" "sudo test -S $socket_path" 2>/dev/null; then
            CONTAINERD_SOCKET="$socket_path"
            return 0
        fi
    done
    
    # Dynamic fallback discovery...
}
```

### Container startup with verification:
```bash
# Use detected socket path
-v $CONTAINERD_SOCKET:/run/containerd/containerd.sock \

# Verify container is running
if ssh "${SSH_ARGS[@]}" "$REMOTE_SUDO docker ps --filter name=$UNREGISTRY_CONTAINER --filter status=running --quiet" | grep -q .; then
    return 0
else
    warning "Unregistry container started but is not running properly, retrying..."
    # Cleanup and retry...
fi
```

### Proxy handling:
```bash
# Store and unset proxy variables
ORIGINAL_HTTP_PROXY="${HTTP_PROXY:-}"
# ... store other proxy vars
unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy

# Perform push operation
# ...

# Restore proxy settings
export HTTP_PROXY="$ORIGINAL_HTTP_PROXY"
# ... restore other proxy vars
```

## Tested Environments

- ✅ Ubuntu 24.04 with Docker 24.0.7 → OpenSUSE Tumbleweed with Docker 28.2.2
- ✅ Works with both standard containerd and Docker-managed containerd installations
- ✅ Handles proxy configurations correctly
- ✅ Tested with large container images (multi-GB)

## Error Scenarios Handled

1. **Socket not found**: Graceful error with helpful message
2. **Container startup failure**: Automatic retry with cleanup
3. **Push failures**: Retry mechanism with exponential backoff
4. **Proxy interference**: Automatic proxy bypass for localhost connections
5. **Permission issues**: Automatic sudo fallback for socket access

## Backward Compatibility

All changes are backward compatible and don't affect existing working configurations. The script will:
- Continue to work with existing `/run/containerd/containerd.sock` setups
- Gracefully handle systems without sudo requirements
- Work with or without proxy configurations

## Before/After

### Before (failing):
```
Get "http://localhost:56354/v2/": read tcp 127.0.0.1:59516->127.0.0.1:56354: read: connection reset by peer
ERROR: Failed to push image.
```

### After (working):
```
✓ Unregistry is listening localhost:55919 on remote host.
✓ Forwarded localhost:57060 to unregistry over SSH connection.
• Pushing 'localhost:57060/docker.io/infiniflow/infinity_builder:centos7_clang18' to unregistry...
[... successful push output ...]
✓ Successfully pushed 'docker.io/infiniflow/infinity_builder:centos7_clang18' to zhichyu@***************
```

---

This fix resolves the connection issues that were preventing `docker pussh` from working reliably across different Linux distributions and Docker configurations, particularly addressing the common case where Docker manages its own containerd instance.
