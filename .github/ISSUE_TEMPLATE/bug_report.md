---
name: Bug Report
about: Create a report to help us improve
title: "[BUG] "
labels: bug
assignees: ""
---

**Describe the bug**

<!-- A clear and concise description of what the bug is. -->

**How to reproduce**

<!-- Steps to reproduce the behavior:

1. Run ...
2. Do ...
-->

**Expected behavior**

<!-- A clear and concise description of what you expected to happen. -->

**Environment:**

- Unregistry versions
  - pussh version (output of `docker pussh --version`):
  - unregistry (Docker image) version:
- OS version: <!-- e.g. macOS 14.5 -->
- Bash version (output of `bash --version`):
- Output of `docker info`:
- For macOS clients, how do you run docker: <!-- e.g. Docker Desktop or Colima, version 1.2.3 -->

**Additional context**

<!-- Add any other context about the problem here. -->
