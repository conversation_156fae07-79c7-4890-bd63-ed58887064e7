Thanks for the review @tonyo! I've addressed all your comments:

1. **Simplified proxy handling**: Added `no_proxy` export at the beginning of the script instead of complex save/restore logic:
   ```bash
   # Ensure localhost connections bypass proxy
   export no_proxy="${no_proxy:-},localhost,127.0.0.1"
   ```

2. **Removed dynamic socket search**: Removed the dynamic containerd socket discovery and kept only the predefined paths list.

3. **Fixed socket detection**: For socket detection, I kept the dual approach (with and without sudo) since `find_containerd_socket()` is called before `check_remote_docker()`, so `REMOTE_SUDO` is not yet available at that point.

4. **Moved comment**: Moved the `DOCKER_PUSH_OPTS` comment to be next to the `docker push` line.

5. **Simplified remote docker pull**: Removed explicit proxy disabling in the remote docker pull command, since we're now using `no_proxy`.

The changes are simpler, more maintainable, and still fix the original issue with containerd socket detection on different Linux distributions.
