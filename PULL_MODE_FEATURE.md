# Add Pull Mode Support to docker-pussh

## Feature Overview

Added bidirectional support to `docker-pussh`, enabling both **push** (local to remote) and **pull** (remote to local) operations. The tool now automatically detects the operation mode based on argument order.

## Usage

### Push Mode (existing functionality)
```bash
# Push local image to remote host
docker pussh [OPTIONS] IMAGE[:TAG] [USER@]HOST[:PORT]

# Examples
docker pussh myimage:latest user@host
docker pussh --platform linux/amd64 myimage:latest host
docker pussh myimage:latest user@host:2222 -i ~/.ssh/id_ed25519
```

### Pull Mode (new functionality)
```bash
# Pull image from remote host to local
docker pussh [OPTIONS] [USER@]HOST[:PORT] IMAGE[:TAG]

# Examples
docker pussh user@host myimage:latest
docker pussh --platform linux/amd64 host myimage:latest
docker pussh user@host:2222 myimage:latest -i ~/.ssh/id_ed25519
```

## How It Works

### Automatic Mode Detection
The tool automatically detects the operation mode by analyzing the argument pattern:

1. **Pull Mode**: If the first argument looks like an SSH address (contains `@`, IP address, or hostname) and the second argument looks like an image name
2. **Push Mode**: If the first argument looks like an image name and the second argument looks like an SSH address
3. **Default**: Falls back to push mode for backward compatibility

### Pull Mode Workflow
1. **Connect** to remote host via SSH
2. **Verify** that the specified image exists on the remote host
3. **Start** unregistry container on remote host
4. **Push** image from remote Docker to unregistry (on remote host)
5. **Forward** unregistry port through SSH tunnel
6. **Pull** image from unregistry to local Docker
7. **Tag** the pulled image with the original name
8. **Cleanup** temporary registry images and containers

## Implementation Details

### Key Functions Added

#### `detect_operation_mode()`
```bash
detect_operation_mode() {
    local arg1="$1"
    local arg2="$2"
    
    # Check if first argument looks like SSH address
    if [[ "$arg1" =~ @ ]] || [[ "$arg1" =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+ ]] || 
       [[ "$arg1" =~ ^[a-zA-Z0-9.-]+$ && ! "$arg1" =~ : ]]; then
        # First arg is SSH address, second is image -> PULL mode
        if [[ "$arg2" =~ : ]] || [[ "$arg2" =~ ^[a-zA-Z0-9._/-]+$ ]]; then
            echo "pull"
            return 0
        fi
    fi
    
    # Default to push mode for backward compatibility
    echo "push"
}
```

#### Enhanced Argument Processing
- Automatic argument swapping for pull mode
- Validation of image existence (local for push, remote for pull)
- Mode-specific error messages and feedback

#### Pull Mode Logic
- Remote-to-unregistry push operation
- Local pull from forwarded unregistry port
- Proper image tagging and cleanup
- Retry mechanisms for both push and pull operations

### Error Handling

#### Pull Mode Specific Errors
- **Image not found on remote**: Validates image exists before starting transfer
- **Remote push failures**: Retry logic with exponential backoff
- **Local pull failures**: Retry logic with proper cleanup
- **Tagging failures**: Graceful error handling with cleanup

#### Backward Compatibility
- All existing push mode functionality preserved
- Default behavior unchanged for existing scripts
- Same command-line options work for both modes

## Testing Results

### Successful Test Cases

#### Pull Mode
```bash
$ ./docker-pussh zhichyu@*************** ubuntu:22.04
 • Connecting to zhichyu@***************...
 • Pull mode: Downloading 'ubuntu:22.04' from zhichyu@***************...
 • Starting unregistry container on remote host...
 ✓ Unregistry is listening localhost:59524 on remote host.
 ✓ Forwarded localhost:64316 to unregistry over SSH connection.
 • Pushing 'ubuntu:22.04' from remote host to unregistry...
 • Pulling 'localhost:64316/ubuntu:22.04' to local Docker...
 ✓ Successfully pulled 'ubuntu:22.04' from zhichyu@***************
```

#### Push Mode (backward compatibility)
```bash
$ ./docker-pussh ubuntu:22.04 zhichyu@***************
 • Connecting to zhichyu@***************...
 • Push mode: Uploading 'ubuntu:22.04' to zhichyu@***************...
 ✓ Successfully pushed 'ubuntu:22.04' to zhichyu@***************
```

### Platform Support
- ✅ Works with `--platform` option for both push and pull modes
- ✅ Supports multi-platform images when containerd image store is enabled
- ✅ Proper proxy handling for both directions

### Environment Compatibility
- ✅ Ubuntu 24.04 ↔ OpenSUSE Tumbleweed
- ✅ Different Docker versions (24.0.7 ↔ 28.2.2)
- ✅ Various containerd configurations

## Benefits

1. **Bidirectional Transfer**: Complete solution for Docker image transfer without external registries
2. **Automatic Mode Detection**: No need for separate commands or flags
3. **Consistent Interface**: Same options and behavior for both directions
4. **Backward Compatible**: Existing scripts continue to work unchanged
5. **Robust Error Handling**: Comprehensive retry logic and cleanup
6. **Efficient Transfer**: Leverages existing unregistry infrastructure

## Use Cases

### Development Workflows
```bash
# Pull latest image from development server
docker pussh dev-server:2222 myapp:latest

# Push updated image back to development server
docker pussh myapp:latest dev-server:2222
```

### CI/CD Pipelines
```bash
# Pull base image from build server
docker pussh build-server myapp-base:latest

# Build and push back to deployment server
docker build -t myapp:$VERSION .
docker pussh myapp:$VERSION deploy-server
```

### Image Migration
```bash
# Migrate images between servers
docker pussh server1 myapp:v1.0
docker pussh myapp:v1.0 server2
```

This enhancement makes `docker-pussh` a complete bidirectional image transfer solution while maintaining full backward compatibility.
